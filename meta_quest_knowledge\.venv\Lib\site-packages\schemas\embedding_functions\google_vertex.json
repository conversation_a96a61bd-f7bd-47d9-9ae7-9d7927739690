{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Google Vertex Embedding Function Schema", "description": "Schema for the Google Vertex embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"model_name": {"type": "string", "description": "The name of the model to use for text embeddings"}, "project_id": {"type": "string", "description": "The Google Cloud project ID"}, "region": {"type": "string", "description": "The Google Cloud region"}, "api_key_env_var": {"type": "string", "description": "Environment variable name that contains your API key for the Google Vertex API"}}, "required": ["api_key_env_var", "model_name", "project_id", "region"], "additionalProperties": false}