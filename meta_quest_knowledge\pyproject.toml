[project]
name = "meta_quest_knowledge"
version = "0.1.0"
description = "Knowledge Example using crewAI"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.10,<=3.13"
dependencies = [
    "crewai[tools]>=0.95.0,<1.0.0",
]

[project.scripts]
meta_quest_knowledge = "meta_quest_knowledge.main:run"
run_crew = "meta_quest_knowledge.main:run"
train = "meta_quest_knowledge.main:train"
replay = "meta_quest_knowledge.main:replay"
test = "meta_quest_knowledge.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
